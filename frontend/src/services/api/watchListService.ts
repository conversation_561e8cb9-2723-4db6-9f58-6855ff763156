import { get, post, put, del, postLongRunning } from './apiClient';
import {
  WatchListItem,
  CreateWatchListRequest,
  UpdateWatchListRequest,
  ReorderWatchListRequest,
  RecalculatePerformanceResponse,
  WatchListUpdateRequest,
  Watch<PERSON>istUpdateResult,
  ApiResponse
} from '../../types/api';

export class WatchListService {

  /**
   * Get all watch list items
   */
  static async getWatchListItems(): Promise<ApiResponse<WatchListItem[]>> {
    return get<WatchListItem[]>('/watchlist');
  }

  /**
   * Get watch list item by ID
   */
  static async getWatchListItemById(id: number): Promise<ApiResponse<WatchListItem>> {
    return get<WatchListItem>(`/watchlist/${id}`);
  }

  /**
   * Create new watch list item
   */
  static async createWatchListItem(request: CreateWatchListRequest): Promise<ApiResponse<WatchListItem>> {
    return post<WatchListItem>('/watchlist', request);
  }

  /**
   * Update existing watch list item
   */
  static async updateWatchListItem(id: number, request: UpdateWatchListRequest): Promise<ApiResponse<WatchListItem>> {
    return put<WatchListItem>(`/watchlist/${id}`, request);
  }

  /**
   * Delete watch list item
   */
  static async deleteWatchListItem(id: number): Promise<ApiResponse<string>> {
    return del<string>(`/watchlist/${id}`);
  }

  /**
   * Update performance metrics for a watch list item
   */
  static async updatePerformance(
    id: number,
    oneMonthPerf?: number,
    threeMonthPerf?: number,
    sixMonthPerf?: number
  ): Promise<ApiResponse<WatchListItem>> {
    return post<WatchListItem>(`/watchlist/${id}/performance`, {
      oneMonthPerformance: oneMonthPerf,
      threeMonthPerformance: threeMonthPerf,
      sixMonthPerformance: sixMonthPerf
    });
  }

  /**
   * Reorder watch list items
   */
  static async reorderWatchListItems(request: ReorderWatchListRequest): Promise<ApiResponse<string>> {
    return put<string>('/watchlist/reorder', request);
  }

  /**
   * Recalculate performance metrics for all watch list items
   */
  static async recalculatePerformance(): Promise<ApiResponse<RecalculatePerformanceResponse>> {
    return post<RecalculatePerformanceResponse>('/watchlist/recalculate-performance', {});
  }

  /**
   * Update OHLCV data for all watch list symbols and recalculate technical indicators
   */
  static async updateOHLCVDataForWatchList(
    request: WatchListUpdateRequest = {}
  ): Promise<ApiResponse<WatchListUpdateResult>> {
    // Use long-running endpoint for potentially time-consuming operations
    return postLongRunning<WatchListUpdateResult>('/watchlist/update-ohlcv-data', request);
  }
}
